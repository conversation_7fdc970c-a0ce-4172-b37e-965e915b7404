<?php

declare(strict_types=1);

namespace App\Controller\Apiv1;

use App\Entity\UserCourseChapter;
use App\Exception\ExcededApiRequestsException;
use App\Exception\InvalidApiKeyException;
use App\Exception\InvalidDateFormatException;
use App\Exception\InvalidDateRangeException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @Route("/api/v1")
 */
class SessionsController extends ApiBaseController
{
    /**
     * @Route("/sessions", methods={"POST"})
     *
     * @throws \Exception
     */
    public function __invoke(): Response
    {
        $apiKey = $this->requestStack->getCurrentRequest()->headers->get(self::API_KEY_FIELD);

        try {
            $this->checkAccess($apiKey);
        } catch (InvalidApiKeyException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_UNAUTHORIZED,
                'data' => ['error' => 'X-API-KEY header is missing or invalid.'],
            ]);
        } catch (ExcededApiRequestsException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_TOO_MANY_REQUESTS,
                'data' => ['error' => 'Too many requests.'],
            ]);
        }

        $requestContent = json_decode($this->requestStack->getCurrentRequest()->getContent(), true);

        $from = $requestContent['date_from'] ?? '';
        $to = $requestContent['date_to'] ?? '';

        try {
            $this->checkDates($from, $to);
        } catch (InvalidDateFormatException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => ['error' => 'The date format is not valid.'],
            ]);
        } catch (InvalidDateRangeException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_REQUESTED_RANGE_NOT_SATISFIABLE,
                'data' => ['error' => 'The date range is not valid.'],
            ]);
        }

        $this->saveRequest($apiKey, 'sessions');

        $dateFrom = new \DateTime($from);
        $dateTo = new \DateTime($to);

        $userCourseChapterRepository = $this->entityManager->getRepository(UserCourseChapter::class);
        $sessions = $userCourseChapterRepository->createQueryBuilder('ucc')
            ->addSelect('ucc.id, ucc.startedAt as userChapterStartedAt, ucc.finishedAt as userChapterFinishedAt')
            ->addSelect('u.code, u.id as userId, u.meta, CONCAT(u.firstName, \' \', u.lastName) as userName, u.email')
            ->addSelect('c.id as courseId, c.name as courseName')
            ->addSelect('ch.id as chapterId, ch.title as chapterName')
            ->addSelect('cat.name as courseCategoryName')
            ->leftJoin('ucc.userCourse', 'uc')
            ->leftJoin('ucc.chapter', 'ch')
            ->leftJoin('uc.course', 'c')
            ->leftJoin('uc.user', 'u')
            ->leftJoin('c.category', 'cat')
            ->andWhere('ucc.finishedAt BETWEEN :from AND :to')
            ->setParameters([
                'from' => $dateFrom,
                'to' => $dateTo,
            ])
            ->getQuery()
            ->getResult();

        $data = [];
        foreach ($sessions as $session) {
            $data[] = [
                'id' => $session['id'],
                'userId' => $session['userId'],
                'userHrp' => $session['meta']['HRP'] ?? '',
                'userCode' => $session['code'],
                'userName' => $session['userName'],
                'userEmail' => $session['email'],
                'courseId' => $session['courseId'],
                'courseName' => $session['courseName'],
                'courseCategoryName' => $session['courseCategoryName'],
                'chapterId' => $session['chapterId'],
                'chapterName' => $session['chapterName'],
                'userChapterStartedAt' => $session['userChapterStartedAt']->format('Y-m-d H:i:s'),
                'userChapterFinishedAt' => $session['userChapterFinishedAt']->format('Y-m-d H:i:s'),
                'timezone' => 'Europe/Madrid', // TODO: get from user
                'assistancePercentage' => 100, // TODO: get from user
                'userCheck' => true, // TODO: get from user
                'tutorCheck' => true, // TODO: get from user
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'data' => $data,
        ]);
    }
}
