<?php

declare(strict_types=1);

namespace App\Controller\Apiv1;

use App\Entity\AnnouncementGroupSession;
use App\Entity\Course;
use App\Entity\TypeCourse;
use App\Entity\User;
use App\Entity\UserCourseChapter;
use App\Exception\ExcededApiRequestsException;
use App\Exception\InvalidApiKeyException;
use App\Exception\InvalidDateFormatException;
use App\Exception\InvalidDateRangeException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @Route("/api/v1")
 */
class CourseActivityController extends ApiBaseController
{
    /**
     * @Route("/course-activity", methods={"POST"})
     *
     * @throws \Exception
     */
    public function __invoke(): Response
    {
        $apiKey = $this->requestStack->getCurrentRequest()->headers->get(self::API_KEY_FIELD);

        try {
            $this->checkAccess($apiKey);
        } catch (InvalidApiKeyException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_UNAUTHORIZED,
                'data' => ['error' => 'X-API-KEY header is missing or invalid.'],
            ]);
        } catch (ExcededApiRequestsException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_TOO_MANY_REQUESTS,
                'data' => ['error' => 'Too many requests.'],
            ]);
        }

        $requestContent = json_decode($this->requestStack->getCurrentRequest()->getContent(), true);

        $from = $requestContent['date_from'] ?? '';
        $to = $requestContent['date_to'] ?? '';
        $courseId = $requestContent['course_id'] ?? null;
        $courseTypeCode = $requestContent['course_type'] ?? null;
        $courseType = null;
        $course = null;

        try {
            $this->checkDates($from, $to);
        } catch (InvalidDateFormatException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => ['error' => 'The date format is not valid.'],
            ]);
        } catch (InvalidDateRangeException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_REQUESTED_RANGE_NOT_SATISFIABLE,
                'data' => ['error' => 'The date range is not valid.'],
            ]);
        }

        if ($courseTypeCode) {
            $courseType = $this->entityManager->getRepository(TypeCourse::class)->findOneBy(['code' => $courseTypeCode, 'active' => true]);
            if (!$courseType) {
                return $this->sendResponse([
                    'status' => Response::HTTP_NOT_FOUND,
                    'data' => ['error' => 'Course type not found.'],
                ]);
            }
        }

        if ($courseId) {
            $course = $this->entityManager->getRepository(Course::class)->find($courseId);
            if (!$course) {
                return $this->sendResponse([
                    'status' => Response::HTTP_NOT_FOUND,
                    'data' => ['error' => 'Course not found.'],
                ]);
            }
        }

        $this->saveRequest($apiKey, 'course-activity');

        $data = [];
        $dateFrom = new \DateTime($from);
        $dateTo = new \DateTime($to);

        if (!$courseTypeCode || TypeCourse::CODE_ONLINE === $courseTypeCode) {
            $data[TypeCourse::CODE_ONLINE] = $this->getOnlineActivity($dateFrom, $dateTo, $course);
        }

        if (!$courseTypeCode || TypeCourse::CODE_ONSITE === $courseTypeCode) {
            $data[TypeCourse::CODE_ONSITE] = $this->getOnsiteActivity($dateFrom, $dateTo, $course);
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'data' => $data,
        ]);
    }

    private function getOnsiteActivity(\DateTime $dateFrom, \DateTime $dateTo, ?Course $course): array
    {
        $announcementGroupSessionRepository = $this->entityManager->getRepository(AnnouncementGroupSession::class);
        $sessions = $announcementGroupSessionRepository->createQueryBuilder('ags')
            ->addSelect('a.id, a.code, a.timezone')
            ->addSelect('c.id as courseId, c.code as courseCode, c.name as courseName')
            ->addSelect('ags.id as sessionId, ags.startAt as sessionStartedAt, ags.finishAt as sessionFinishedAt, ags.assistance, ags.studentAssistance, ags.type, ags.session_number, ags.timezone, ags.place')
            ->addSelect('cat.name as courseCategoryName')
            ->leftJoin('ags.announcementGroup', 'ag')
            ->leftJoin('ag.announcement', 'a')
            ->leftJoin('a.course', 'c')
            ->leftJoin('c.category', 'cat')
            ->andWhere('ags.finishAt BETWEEN :from AND :to')
            ->setParameters([
                'from' => $dateFrom,
                'to' => $dateTo,
            ])
            ->getQuery()
            ->getResult();

        $data = [];
        foreach ($sessions as $session) {
            if (!$session['assistance']) {
                continue;
            }

            foreach ($session['assistance'] as $assistance) {
                $user = $this->entityManager->find(User::class, $assistance['id']);
                $studentAssistance = $this->getStudentAssistance($session, $user->getId());
                $data[] = [
                    'id' => $session['id'],
                    'userId' => $user->getId(),
                    'userHrp' => $user->getMeta()['HRP'] ?? '',
                    'userCode' => $user->getCode(),
                    'userName' => $user->getFullName(),
                    'userEmail' => $user->getEmail(),
                    'courseId' => $session['courseId'],
                    'courseName' => $session['courseName'],
                    'courseCategoryName' => $session['courseCategoryName'],
                    'sessionId' => $session['sessionId'],
                    'sessionStartedAt' => $session['sessionStartedAt']->format('Y-m-d H:i:s'),
                    'sessionFinishedAt' => $session['sessionFinishedAt']->format('Y-m-d H:i:s'),
                    'timezone' => $session['timezone'],
                    'assistance' => $assistance['assistance'],
                    'assistancePercentage' => $assistance['percent'],
                    'studentAssistance' => $studentAssistance['assistance'] ?? null,
                    //                    'studentAssistanceInfo' => $studentAssistance['info'] ?? null,
                ];
            }
        }

        return $data;
    }

    private function getOnlineActivity(\DateTime $dateFrom, \DateTime $dateTo, ?Course $course): array
    {
        $userCourseChapterRepository = $this->entityManager->getRepository(UserCourseChapter::class);
        $sessions = $userCourseChapterRepository->createQueryBuilder('ucc')
            ->addSelect('ucc.id, ucc.startedAt as userChapterStartedAt, ucc.finishedAt as userChapterFinishedAt, ucc.timeSpent as userTimeSpent')
            ->addSelect('u.code, u.id as userId, u.meta, CONCAT(u.firstName, \' \', u.lastName) as userName, u.email, u.timezone')
            ->addSelect('c.id as courseId, c.name as courseName')
            ->addSelect('ch.id as chapterId, ch.title as chapterName')
            ->addSelect('cat.name as courseCategoryName')
            ->leftJoin('ucc.userCourse', 'uc')
            ->leftJoin('ucc.chapter', 'ch')
            ->leftJoin('uc.course', 'c')
            ->leftJoin('uc.user', 'u')
            ->leftJoin('c.category', 'cat')
            ->andWhere('ucc.finishedAt BETWEEN :from AND :to')
            ->setParameters([
                'from' => $dateFrom,
                'to' => $dateTo,
            ])
            ->getQuery()
            ->getResult();

        $data = [];
        foreach ($sessions as $session) {
            $data[] = [
                'id' => $session['id'],
                'userId' => $session['userId'],
                'userHrp' => $session['meta']['HRP'] ?? '',
                'userCode' => $session['code'],
                'userName' => $session['userName'],
                'userEmail' => $session['email'],
                'courseId' => $session['courseId'],
                'courseName' => $session['courseName'],
                'courseCategoryName' => $session['courseCategoryName'],
                'chapterId' => $session['chapterId'],
                'chapterName' => $session['chapterName'],
                'userChapterStartedAt' => $session['userChapterStartedAt']->format('Y-m-d H:i:s'),
                'userChapterFinishedAt' => $session['userChapterFinishedAt']->format('Y-m-d H:i:s'),
                'userTimeSpent' => $session['userTimeSpent'],
                'timezone' => $session['timezone'],
            ];
        }

        return $data;
    }

    private function getStudentAssistance($session, $userId)
    {
        foreach ($session['studentAssistance'] as $assistance) {
            if ($assistance['id'] === $userId) {
                return $assistance;
            }
        }

        return null;
    }
}
